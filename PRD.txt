Product Requirements Document: Fleet Management Aggregator Platform (Demo Version)
1. Introduction
This document outlines the requirements for a demo version of a Fleet Management Aggregator Platform. The platform aims to connect last-mile delivery guys (e.g., <PERSON>wiggy, Zomato) with fleet suppliers (middlemen like <PERSON><PERSON> and individual solo riders) using 2-wheelers across various locations in India. This demo will showcase the core matching and commitment functionalities ONLY. It will not include logic implementation. It’s entire goal is to understand how the USER JOURNEY will look like.
2. Goals
* To demonstrate a functional proof-of-concept for matching delivery demand with available supply.
* To showcase the core user journeys for 
   * Companies that require the service, 
   * middlemen suppliers, and 
   * solo riders.
* [Not right now] To validate the basic logic of the matching engine and commitment lock-in process.
3. Problem Statement
* Market Gap: The last-mile delivery ecosystem faces a gap: To match high-volume, fluctuating delivery demand from aggregators (like Swiggy, Zomato, Blinkit) with the decentralized and often fragmented supply of 2-wheeler riders. 
* [NOT IMPORTANT FOR DEV - feel free to read] Existing Pain Points:
   * Fragmented Supply: Rider supply is scattered across numerous small fleet managers and individual riders, making it hard for demand creators to tap into a consolidated pool.
   * Lack of Real-Time Matching: Manual or semi-automated processes for finding and allocating riders are slow, especially during peak demand, leading to delays and unfulfilled orders.
   * Inefficiencies in Manual Coordination: Significant time and resources are wasted by demand creators and suppliers in phone calls, spreadsheets, and messages to coordinate requirements and availability.
   * Unverified Riders (especially ad-hoc): Difficulty in quickly verifying the credentials and reliability of riders sourced on an ad-hoc basis, posing risks to service quality and security.
   * Ad-hoc Hiring During Peak Loads: Demand creators struggle to quickly scale their rider fleet up or down to match fluctuating demand, often resorting to expensive or unreliable last-minute hiring. Middlemen suppliers also struggle to find consistent demand to keep their riders engaged.
________________


4. [Need to develop how any of the following personas/users will interact with the platform once] User Personas
* Persona 1: Buyer (Logistics Demand Creator)
   * Represents: Operations/Logistics Manager at companies like Swiggy, Zomato, Blinkit.
   * [NA] Concerns: Ensuring timely delivery fulfillment, maintaining service quality, managing costs, finding reliable riders quickly, especially during peak hours or for specific micro-locations.
   * Needs from Platform:
      * Easily post bulk delivery requirements (quantity, location, time slots).
      * Specify desired rate or budget.
      * Ensure rider reliability and basic verification.
      * Get commitments fulfilled efficiently without extensive manual follow-up.
      * Transparent pricing and confirmation.
   * [fields in a form to take it as input] Quote: "I need n = 100 riders in LOCATION Koramangala for the TIME SLOT 7 PM - 10 PM slot tomorrow, and they must be reliable. How quickly can I get this confirmed at a competitive rate?"

   * Persona 2: Supplier (Middlemen Fleet Manager)
   * Represents: Owner/Operator of a Yana-like business managing 50-100+ 2-wheeler riders.
   * [NA] Concerns: Maximizing rider utilization, securing consistent business, fulfilling committed deliveries, managing rider payments and performance.
   * Needs from Platform:
   * View available delivery requirements relevant to their operational areas.
   * Bid for partial or full fulfillment of requirements.
   * Clear communication on commitment and expectations.
   * A simple way to assign their riders to confirmed jobs.
   * [fields in a form to take it as input] Quote: "I have 20 riders free tomorrow evening in Indiranagar. I need to find them work to cover their fixed costs and make a profit. I can commit to reliable delivery."

      * Persona 3: Solo Rider
      * Represents: Individual 2-wheeler delivery agent looking for flexible earning opportunities.
      * Concerns: Finding gigs easily, fair pay, clear instructions, flexible working hours, especially during peak times or to supplement primary income.
      * Needs from Platform:
      * See available ad-hoc delivery opportunities filtered by location, time, and language preference.
      * Quickly apply for jobs they are interested in.
      * Clear confirmation and details of the accepted gig.
      * (Future scope: Timely payment, good rating leading to more opportunities).
      * [fields in a form to take it as input] Quote: "I'm free on Saturday afternoon and want to make some extra money. Are there any delivery gigs available near Marathahalli between 1 PM and 5 PM that require speaking Kannada?"
________________


5. Critical User Journeys (CUJ)
      * CUJ 1: Demand Creator Posts a Requirement
      1. Logistics Larry (Swiggy) logs into the platform.
      2. Navigates to "Post New Requirement."
      3. Fills in the form:
      * Quantity: Number of riders needed (e.g., 50).
      * Location(s): Target delivery area(s)/pin codes (e.g., Koramangala, Bangalore).
      * Time Window: Start Date, End Date, Start Time, End Time (e.g., 2023-11-01, 19:00 to 22:00). Smallest unit 1 hour.
      * Rate: Proposed rate per hour per rider (e.g., INR 150/hr).
      * Language (Optional): Preferred rider language (e.g., Kannada, Hindi).
      4. Submits the requirement.
      5. [ASSUME] System confirms posting and the requirement becomes visible (anonymously) to suppliers.

         * CUJ 2: Middleman Supplier Views and Bids for Requirements
         1. Fleet Fiona (Yana) logs into the platform.
         2. Navigates to "Available Requirements."
         3. Views a list of requirements (demand creator's brand is not visible).
         4. Filters requirements by Location, Time Window.
         5. Selects a relevant requirement posted by "Anonymous Buyer."
         6. Chooses to bid:
         * Fulfillment Type: Full (e.g., all 50 riders) or Partial (e.g., 30 out of 50 riders).
         * Proposed Rate (if different, for demo can be fixed to buyer's rate): Enters their bid rate.
         * Commitment: Agrees to terms.
         7. Submits the bid.
         8. Bid is registered in the system.

            * CUJ 3: Solo Rider Views and Applies for Ad-hoc Opportunities
            1. Rider Raj logs into the platform (simple mobile-web interface).
            2. Navigate to "Find Gigs."
            3. Views a list of available ad-hoc opportunities (typically smaller chunks of unfulfilled demand or specific short-term needs).
            4. Filters opportunities by Location, Time Slot, Language.
            5. Selects an opportunity (e.g., 1 rider needed in Marathahalli, 13:00-15:00, Kannada speaking).
            6. Clicks "Apply" or "Express Interest."
            7. Application is registered in the system.

               * [PHASE 2] CUJ 4: Matching Engine Suggests Fulfillment Combinations
               1. A requirement from Swiggy (e.g., 50 riders) is in the system.
               2. Yana bids for partial fulfillment (e.g., 30 riders).
               3. Multiple Solo Riders apply for gigs that align with the remaining need (e.g., 20 individual slots).
               4. The Matching Engine (logic):
               * Prioritizes bids from Middlemen for larger chunks.
               * Identifies the gap (50 - 30 = 20 riders).
               * Selects suitable Solo Riders based on their application, location, time, (mocked) reliability.
               * Proposes a combination to Swiggy: "Yana (30 riders) + 20 selected Solo Riders."
               5. (For Demo) The system automatically presents this "best fit" combination to the demand creator if full requirement can be met.

                  * CUJ 5: Negotiation & Confirmation (Simplified for Demo)
                  1. Swiggy (Logistics Larry) views the proposed fulfillment plan from the Matching Engine (Yana for 30, 20 Solo Riders).
                  2. If satisfied, Swiggy clicks "Accept & Lock-in Commitment."
                  3. System:
                  * Notifies Yana: "Your bid for 30 riders for Requirement #XYZ is Confirmed." Yana then internally assigns their riders.
                  * Notifies the 20 selected Solo Riders: "Your application for gig #ABC is Confirmed."
                  4. The requirement status changes to "Locked-in" or "Fulfilled."

                     * CUJ 6: Tracking Reliability of Riders (Basic Implementation for Demo)
                     1. After a (mocked) delivery slot completion:
                     * Swiggy (Logistics Larry) gets an option to provide simple feedback for the fulfillment (e.g., "Successful," "Minor Issues," "Major Issues").
                     * This feedback can be associated with Yana's overall fulfillment and, if possible, at a (mocked) granular level for solo riders involved.
                     2.                      3. System: A basic reliability score (e.g., 1-5 stars or a percentage) is updated for Yana (as a supplier) and for individual Solo Riders. This score is visible internally (e.g., to Admin, and potentially influences future matching).


________________

6. Platform Modules (for Demo) Based on Above
                     * User Management & Authentication Module:
                     * Simple login/registration for Buyer, Supplier (Middleman), Solo Rider. 
- Can be with simple usernames like Swiggy, Yana and Ram on three different URL paths as this is just a demo project
                        * Requirement Posting Module (for Buyer: Swiggy, Zomato)
                        * Form to create new delivery requirements (quantity, location, time, rate, language).
                        * Dashboard to view status of posted requirements (Pending, Bidding, Locked-in, Completed - mocked).
                        * Bidding Module (for Supplier: Yana, other middlemen)
                        * Interface to view anonymized requirements.
                        * Filters for requirements (location, time).
                        * Form to submit bids (full/partial, proposed rate - can be fixed for demo).
                        * Dashboard to track status of their bids (Submitted, Accepted, Rejected).
                        * Solo Rider Interface (for Solo Riders)
                        * Simple job board to view available ad-hoc opportunities.
                        * Filters (location, time slot, language).
                        * Button to "Apply" for opportunities.
                        * View status of applications (Applied, Confirmed, Completed - mocked).
                        * Display basic profile and (mocked) reliability rating.
                        * [NOT REQUIRED - A DUMMY MATCHING IS GOOD ENOUGH] Matching Engine (Core Logic - Backend)
                        * Algorithm to analyze posted requirements and available bids/applications.
                        * Logic to combine partial bids from middlemen and applications from solo riders to achieve full or maximum possible fulfillment.
                        * Basic pricing strategy (e.g., accept buyer's rate, or if supplier bids, prioritize lower valid bids for demo).
                        * Outputs a proposed fulfillment plan.
                        * Commitment Lock-in + Transaction Layer (Simplified)
                        * Mechanism for Buyer to "Accept" a proposed fulfillment plan.
                        * Triggers notifications to confirmed Suppliers and Solo Riders.
                        * Updates the status of the requirement and bids/applications.
                        * (Mocked) Transaction record: Buyer, Supplier(s), agreed rate, quantity, time. No actual payment processing.
                        * Admin View (Optional, but Recommended for Demo)
                        * Dashboard with a real-time (or near real-time) overview of:
                        * Total active requirements.
                        * Fulfillment ratio (e.g., 70% of riders for requirement X are matched).
                        * List of users and their roles.
                        * (Mocked) Reliability scores for suppliers and solo riders.
                        * (Mocked) Location-based heatmaps showing demand vs. supply density.
7. Constraints
                        * Demo Version Focus: This PRD is strictly for a demo version. Features beyond core matching and commitment are out of scope.THE UI AND UX MUST HAVE EXTREME EMPHASIS.
                        * Mocked External Integrations:
                        * Swiggy’s/Zomato's job posting API will be simulated by manual input via the platform's UI as they are out of scope.
                        * Payment gateways and processing are out of scope. 
                        * Real-time GPS tracking of riders is out of scope.
                        * Background verification (eKYC) for riders is out of scope (assume riders are pre-vetted for demo).
                        * Flexible Time Units: The system must allow requirement posting and rider availability in time units with a minimum granularity of 1 hour, extendable to daily, weekly, or monthly blocks.
                        * Extreme emphasis on UI/UX: The focus is on functional flows. UI/UX will be clean and usable and as close to Prod polished. Use standard web components.
                        * No Geofencing/Advanced Location Services: Location will be based on predefined areas/pin codes rather than complex geofencing.
                        * Single Currency: INR will be used.
                        * Limited Scalability Testing: The demo will not be subjected to rigorous performance or scalability testing for thousands of concurrent users. [NO TESTING NEEDED]
________________


8. Considerations - Edge cases to factor in
                        * UI/UX - Dummy Real-time Visibility of Job Postings: Middlemen (Yana) and Solo Riders should see newly posted relevant requirements/opportunities with minimal delay (simulated real-time for demo) .
                        * Anonymity of Demand Creators: The identity of the demand creator (Swiggy, Zomato) must NOT be visible to suppliers (Yana, Solo Riders) during the bidding/application phase. They will see "Anonymous Buyer" or similar. Identity may be revealed post-confirmation if necessary (but can be kept anonymous for demo).
                        * Basic Filters [Brute force application of any searching algo is enough]:
                        * For Suppliers/Riders: Location (City, Area/Pincode), Time Slot, Language.
                        * (Future/Admin visible): Vehicle Type (default 2-wheeler for demo), License (assumed valid), Reliability Score (mocked).
                        * Time-Slot Creation Logic:
                        * Buyers define a date range and a daily time window (e.g., Nov 1 - Nov 7, 7 PM - 10 PM daily).
                        * The system should be able to break this down into hourly slots if needed for matching with solo rider availability. Smallest definable unit for a requirement or availability is 1 hour.
                        * [Out of Scope] No Incentivization/Penalties: Complex logic for rider incentives, surge pricing based on demand, or penalties for non-commitment/no-shows is out of scope for the demo.
                        * Data Persistence: Basic data (users, requirements, bids, confirmations) should be persisted - HARDCODED.
________________


9. Success Criteria (for Demo)
The demo will be considered successful if the following can be demonstrated:
                        1. Demand Creation: At least one demand requirement can be successfully posted by a "Swiggy" persona user, specifying quantity, location, time window, rate, and language.
                        2. Middleman Bidding & Commitment:
                        * A "Yana" persona user can view this anonymized requirement.
                        * "Yana" can successfully apply for partial fulfillment of the requirement.
                        * "Yana" can (notionally) commit specific riders from their pool (simulated in the system).
                        3. Solo Rider Application:
                        * At least one "Solo Rider" persona user can view a relevant ad-hoc opportunity derived from the remaining part of Swiggy's requirement (or a separate small requirement).
                        * The solo rider can successfully apply for this opportunity.
                        4. Matching & Lock-in:
                        * The system (either via Matching Engine logic or admin intervention for demo simplicity) proposes a fulfillment plan combining Yana's partial bid and the solo rider's application to meet Swiggy's requirement.
                        * The "Swiggy" persona user can view this proposed plan and confirm/lock-in the commitment.
                        * "Yana" and the "Solo Rider" receive a notification of confirmation.
                        5. Basic Reliability Tracking: A (mocked) reliability score for the involved Yana (supplier) and solo rider can be updated post-fulfillment (manually inputted feedback).
                        6. (Optional but good) Admin View: Admin can see an overview of the requirement, its fulfillment status, and the involved parties.